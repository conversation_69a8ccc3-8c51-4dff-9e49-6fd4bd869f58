#!/bin/bash

echo "Starting Liquibase migration with main context..."
echo "Using configuration from liquibase.local.properties (if exists) or liquibase.properties"
echo ""

# Run the Liquibase update command
liquibase --defaultsFile=./liquibase.local.properties --changelog-file=changelog.yaml --contexts="main" update

# Capture the exit code
EXIT_CODE=$?

echo ""
if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Migration completed successfully!"
else
    echo "❌ Migration failed with exit code: $EXIT_CODE"
fi

echo ""
echo "Press any key to continue..."
read -n 1 -s
