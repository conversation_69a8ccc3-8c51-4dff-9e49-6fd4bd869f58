-- liquibase formatted sql

-- changeset redmi:1755871212748-67 contextFilter:"main" splitStatements:false
ALTER TABLE message_details DROP FOREIGN KEY FK_Message_Details_Notification_Organization_Id;

-- changeset redmi:1755871212748-68 contextFilter:"main" splitStatements:false
ALTER TABLE message_queue DROP FOREIGN KEY FK_Message_Queue_Message_Details_MessageDetailsId;

-- changeset redmi:1755871212748-69 contextFilter:"main" splitStatements:false
ALTER TABLE notification DROP FOREIGN KEY FK_Notification_NotificationType_NotificationTypeId;

-- changeset redmi:1755871212748-70 contextFilter:"main" splitStatements:false
ALTER TABLE notification DROP FOREIGN KEY FK_Notification_Organization_OrganizationId;

-- changeset redmi:1755871212748-71 contextFilter:"main" splitStatements:false
ALTER TABLE notification_organizations DROP FOREIGN KEY FK_Notification_Organizations_Notification_NotificationId;

-- changeset redmi:1755871212748-72 contextFilter:"main" splitStatements:false
ALTER TABLE notification_organizations DROP FOREIGN KEY FK_Notification_Organizations_Organization_OrganizationId;

-- changeset redmi:1755871212748-73 contextFilter:"main" splitStatements:false
ALTER TABLE notification_subscribers DROP FOREIGN KEY FK_Notification_Subscribers_Notification_Organization_Id;

-- changeset redmi:1755871212748-74 contextFilter:"main" splitStatements:false
ALTER TABLE notification_subscribers DROP FOREIGN KEY FK_Notification_Subscribers_Subscriber_SubscriberId;

-- changeset redmi:1755871212748-75 contextFilter:"main" splitStatements:false
ALTER TABLE notification_subscription DROP FOREIGN KEY FK_Notification_Subscription_Service_Organization;

-- changeset redmi:1755871212748-76 contextFilter:"main" splitStatements:false
ALTER TABLE notification_subscription DROP FOREIGN KEY FK_Notification_Subscription_User;

-- changeset redmi:1755871212748-77 contextFilter:"main" splitStatements:false
ALTER TABLE notification DROP FOREIGN KEY FK_Notification_User_UserId;

-- changeset redmi:1755871212748-78 contextFilter:"main" splitStatements:false
ALTER TABLE policy DROP FOREIGN KEY FK_Policy_CreatedBy_User_UserId;

-- changeset redmi:1755871212748-79 contextFilter:"main" splitStatements:false
ALTER TABLE policy DROP FOREIGN KEY FK_Policy_ScheduleId_Schedule_ScheduleId;

-- changeset redmi:1755871212748-80 contextFilter:"main" splitStatements:false
ALTER TABLE policy DROP FOREIGN KEY FK_Policy_UpdatedBy_User_UserId;

-- changeset redmi:1755871212748-81 contextFilter:"main" splitStatements:false
ALTER TABLE `role` DROP FOREIGN KEY FK_Role_OrganizationId_Organization_OrganizationId;

-- changeset redmi:1755871212748-82 contextFilter:"main" splitStatements:false
ALTER TABLE service_configurations DROP FOREIGN KEY FK_Service_Configurations_Service_ServiceId;

-- changeset redmi:1755871212748-83 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization DROP FOREIGN KEY FK_User_Organization_Organization_OrganizationId;

-- changeset redmi:1755871212748-84 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization DROP FOREIGN KEY FK_User_Organization_Service_UserId;

-- changeset redmi:1755871212748-85 contextFilter:"main" splitStatements:false
ALTER TABLE device_folder DROP FOREIGN KEY FK_device_folder_PolicyId_Policy_PolicyId;

-- changeset redmi:1755871212748-86 contextFilter:"main" splitStatements:false
ALTER TABLE services_organizations DROP FOREIGN KEY FK_services_organizations_Organization_OrganizationId;

-- changeset redmi:1755871212748-87 contextFilter:"main" splitStatements:false
ALTER TABLE services_organizations DROP FOREIGN KEY FK_services_organizations_Service_ServiceId;

-- changeset redmi:1755871212748-88 contextFilter:"main" splitStatements:false
ALTER TABLE device_alert DROP FOREIGN KEY fk_device_alert_policy_device_alert;

-- changeset redmi:1755871212748-89 contextFilter:"main" splitStatements:false
ALTER TABLE device_alert DROP FOREIGN KEY fk_device_alert_user;

-- changeset redmi:1755871212748-90 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert DROP FOREIGN KEY fk_policy_device_alert_policy_device_alert_type;

-- changeset redmi:1755871212748-91 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert DROP FOREIGN KEY fk_policy_device_alert_policy_policy_id;

-- changeset redmi:1755871212748-92 contextFilter:"main" splitStatements:false
ALTER TABLE schedule DROP FOREIGN KEY schedule_ibfk_1;

-- changeset redmi:1755871212748-93 contextFilter:"main" splitStatements:false
ALTER TABLE schedule DROP FOREIGN KEY schedule_ibfk_2;

-- changeset redmi:1755871212748-94 contextFilter:"main" splitStatements:false
ALTER TABLE schedule DROP FOREIGN KEY schedule_ibfk_3;

-- changeset redmi:1755871212748-95 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization_mapping DROP FOREIGN KEY user_organization_mapping_ibfk_1;

-- changeset redmi:1755871212748-59 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD UserId INT DEFAULT null NULL;

-- changeset redmi:1755871212748-60 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD OrganizationId INT DEFAULT null NULL;

-- changeset redmi:1755871212748-61 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD IsActive BIT DEFAULT 0 NOT NULL;

-- changeset redmi:1755871212748-62 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD IsDefault BIT DEFAULT 0 NOT NULL;

-- changeset redmi:1755871212748-63 contextFilter:"main" splitStatements:false
CREATE INDEX IX_User_Organization_Mapping_OrganizationId ON User_Organization_Mapping(OrganizationId);

-- changeset redmi:1755871212748-64 contextFilter:"main" splitStatements:false
CREATE INDEX IX_User_Organization_Mapping_UserId ON User_Organization_Mapping(UserId);

-- changeset redmi:1755871212748-65 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_Organization_OrganizationId FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:1755871212748-66 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_User_UserId FOREIGN KEY (UserId) REFERENCES User (UserId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:1755871212748-96 contextFilter:"main" splitStatements:false
ALTER TABLE configuration DROP KEY Category;

-- changeset redmi:1755871212748-97 contextFilter:"main" splitStatements:false
ALTER TABLE user DROP KEY Email;

-- changeset redmi:1755871212748-98 contextFilter:"main" splitStatements:false
ALTER TABLE configurationvalues DROP KEY Name_ConfigurationId;

-- changeset redmi:1755871212748-99 contextFilter:"main" splitStatements:false
ALTER TABLE configuration_value_organization DROP KEY OrganizationId_ConfigurationValuesId;

-- changeset redmi:1755871212748-100 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization DROP KEY UserId_OrganizationId;

-- changeset redmi:1755871212748-101 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert DROP KEY uq_policy_device_alert_type_id_policy_id;

-- changeset redmi:1755871212748-102 contextFilter:"main" splitStatements:false
ALTER TABLE device_alert DROP KEY ux_device_alert_pda_id_a_id_created_on_resource_instance;

-- changeset redmi:1755871212748-103 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert_type DROP KEY ux_name;

-- changeset redmi:1755871212748-104 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert_type DROP KEY ux_type_id;

-- changeset redmi:1755871212748-105 contextFilter:"main" splitStatements:false
DROP TABLE configuration_value_organization;

-- changeset redmi:1755871212748-106 contextFilter:"main" splitStatements:false
DROP TABLE device_alert;

-- changeset redmi:1755871212748-107 contextFilter:"main" splitStatements:false
DROP TABLE message_details;

-- changeset redmi:1755871212748-108 contextFilter:"main" splitStatements:false
DROP TABLE message_queue;

-- changeset redmi:1755871212748-109 contextFilter:"main" splitStatements:false
DROP TABLE notification;

-- changeset redmi:1755871212748-110 contextFilter:"main" splitStatements:false
DROP TABLE notification_organizations;

-- changeset redmi:1755871212748-111 contextFilter:"main" splitStatements:false
DROP TABLE notification_subscribers;

-- changeset redmi:1755871212748-112 contextFilter:"main" splitStatements:false
DROP TABLE notification_subscription;

-- changeset redmi:1755871212748-113 contextFilter:"main" splitStatements:false
DROP TABLE notification_type;

-- changeset redmi:1755871212748-114 contextFilter:"main" splitStatements:false
DROP TABLE policy;

-- changeset redmi:1755871212748-115 contextFilter:"main" splitStatements:false
DROP TABLE policy_device_alert;

-- changeset redmi:1755871212748-116 contextFilter:"main" splitStatements:false
DROP TABLE policy_device_alert_type;

-- changeset redmi:1755871212748-117 contextFilter:"main" splitStatements:false
DROP TABLE regex_filter;

-- changeset redmi:1755871212748-118 contextFilter:"main" splitStatements:false
DROP TABLE schedule;

-- changeset redmi:1755871212748-119 contextFilter:"main" splitStatements:false
DROP TABLE service;

-- changeset redmi:1755871212748-120 contextFilter:"main" splitStatements:false
DROP TABLE service_configurations;

-- changeset redmi:1755871212748-121 contextFilter:"main" splitStatements:false
DROP TABLE services_organizations;

-- changeset redmi:1755871212748-122 contextFilter:"main" splitStatements:false
DROP TABLE user_organization;

-- changeset redmi:1755871212748-123 contextFilter:"main" splitStatements:false
ALTER TABLE device_folder DROP COLUMN `Description`;

-- changeset redmi:1755871212748-124 contextFilter:"main" splitStatements:false
ALTER TABLE configuration DROP COLUMN IsUserVisible;

-- changeset redmi:1755871212748-125 contextFilter:"main" splitStatements:false
ALTER TABLE device_folder DROP COLUMN PolicyId;

-- changeset redmi:1755871212748-126 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization_mapping DROP COLUMN UserOrganizationId;

-- changeset redmi:1755871212748-127 contextFilter:"main" splitStatements:false
ALTER TABLE user DROP COLUMN is_api_user;

-- changeset redmi:1755871212748-128 contextFilter:"main" splitStatements:false
DROP INDEX UserOrganizationId_RoleId ON user_organization_mapping;

-- changeset redmi:1755871212748-1 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` ALTER AllowSubOrg SET DEFAULT 0;

-- changeset redmi:1755871212748-2 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` ALTER AllowWhiteLabel SET DEFAULT 0;

-- changeset redmi:1755871212748-3 contextFilter:"main" splitStatements:false
ALTER TABLE OrganizationMapping MODIFY Application ENUM('CloudInfra', 'ConnectWise');

-- changeset redmi:1755871212748-4 contextFilter:"main" splitStatements:false
ALTER TABLE OrganizationMapping MODIFY Application ENUM('CloudInfra', 'ConnectWise') NOT NULL;

-- changeset redmi:1755871212748-5 contextFilter:"main" splitStatements:false
ALTER TABLE OrganizationMapping ALTER Application DROP DEFAULT;

-- changeset redmi:1755871212748-6 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY AvailableTo INT NULL;

-- changeset redmi:1755871212748-7 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER AvailableTo SET DEFAULT (NULL);

-- changeset redmi:1755871212748-8 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping MODIFY CreatedBy INT NULL;

-- changeset redmi:1755871212748-9 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ALTER CreatedBy SET DEFAULT (NULL);

-- changeset redmi:1755871212748-10 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-11 contextFilter:"main" splitStatements:false
ALTER TABLE User MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-12 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-13 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-14 contextFilter:"main" splitStatements:false
ALTER TABLE ac_cw_vm_map MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-15 contextFilter:"main" splitStatements:false
ALTER TABLE contract_signoff MODIFY DateSigned datetime;

-- changeset redmi:1755871212748-16 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY `Description` LONGTEXT;

-- changeset redmi:1755871212748-17 contextFilter:"main" splitStatements:false
ALTER TABLE User MODIFY Email VARCHAR(255) NULL;

-- changeset redmi:1755871212748-18 contextFilter:"main" splitStatements:false
ALTER TABLE User ALTER Email SET DEFAULT null;

-- changeset redmi:1755871212748-19 contextFilter:"main" splitStatements:false
ALTER TABLE Configuration ALTER IsActive SET DEFAULT 1;

-- changeset redmi:1755871212748-20 contextFilter:"main" splitStatements:false
ALTER TABLE User ALTER IsActive SET DEFAULT 1;

-- changeset redmi:1755871212748-21 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel ALTER IsActive SET DEFAULT 1;

-- changeset redmi:1755871212748-22 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ALTER IsApproved SET DEFAULT 0;

-- changeset redmi:1755871212748-23 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` ALTER IsPartner SET DEFAULT 0;

-- changeset redmi:1755871212748-24 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY IsRestricted TINYINT(1);

-- changeset redmi:1755871212748-25 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY IsRestricted TINYINT(1) NULL;

-- changeset redmi:1755871212748-26 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER IsRestricted SET DEFAULT 0;

-- changeset redmi:1755871212748-27 contextFilter:"main" splitStatements:false
ALTER TABLE ConfigurationValues ALTER IsSecret SET DEFAULT 0;

-- changeset redmi:1755871212748-28 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` ALTER IsVerified SET DEFAULT 0;

-- changeset redmi:1755871212748-29 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY Name LONGTEXT;

-- changeset redmi:1755871212748-30 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY Name LONGTEXT NULL;

-- changeset redmi:1755871212748-31 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER Name SET DEFAULT null;

-- changeset redmi:1755871212748-32 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY OrganizationId INT NULL;

-- changeset redmi:1755871212748-33 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER OrganizationId SET DEFAULT (NULL);

-- changeset redmi:1755871212748-34 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY Permissions BLOB NULL;

-- changeset redmi:1755871212748-35 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER Permissions SET DEFAULT (NULL);

-- changeset redmi:1755871212748-36 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping MODIFY RoleId INT NULL;

-- changeset redmi:1755871212748-37 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ALTER RoleId SET DEFAULT (NULL);

-- changeset redmi:1755871212748-38 contextFilter:"main" splitStatements:false
ALTER TABLE ConfigurationValues MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-39 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-40 contextFilter:"main" splitStatements:false
ALTER TABLE User MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-41 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-42 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-43 contextFilter:"main" splitStatements:false
ALTER TABLE ac_cw_vm_map MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-44 contextFilter:"main" splitStatements:false
ALTER TABLE ConfigurationValues MODIFY Value VARCHAR(65000);

-- changeset redmi:1755871212748-45 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_runlog_entry MODIFY createdAt timestamp;

-- changeset redmi:1755871212748-46 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_compute MODIFY endDate timestamp;

-- changeset redmi:1755871212748-47 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_ipaddress MODIFY endDate timestamp;

-- changeset redmi:1755871212748-48 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_networkbytes MODIFY endDate timestamp;

-- changeset redmi:1755871212748-49 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_primarystorage MODIFY endDate timestamp;

-- changeset redmi:1755871212748-50 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_secondarystorage MODIFY endDate timestamp;

-- changeset redmi:1755871212748-51 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_runlog MODIFY endTime timestamp;

-- changeset redmi:1755871212748-52 contextFilter:"main" splitStatements:false
ALTER TABLE ac_cw_vm_map ALTER isFormula SET DEFAULT 0;

-- changeset redmi:1755871212748-53 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_compute MODIFY startDate timestamp;

-- changeset redmi:1755871212748-54 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_ipaddress MODIFY startDate timestamp;

-- changeset redmi:1755871212748-55 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_networkbytes MODIFY startDate timestamp;

-- changeset redmi:1755871212748-56 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_primarystorage MODIFY startDate timestamp;

-- changeset redmi:1755871212748-57 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_secondarystorage MODIFY startDate timestamp;

-- changeset redmi:1755871212748-58 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_runlog MODIFY startTime timestamp;

